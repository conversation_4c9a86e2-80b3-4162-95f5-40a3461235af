/**
 * 浅色调现代设计系统
 * 优雅、清新、具有美感的UI设计规范
 */

/* ================== 主题色彩系统 ================== */

/* 主色调 - 清新蓝绿色系 */
$primary-color: #00b4d8;
$primary-light: #90e0ef;
$primary-dark: #0077b6;
$primary-gradient: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);

/* 辅助色系 */
$secondary-color: #8b5cf6;
$accent-color: #06ffa5;
$warning-color: #f59e0b;
$danger-color: #ef4444;
$success-color: #10b981;

/* 中性色系 - 优雅灰色调 */
$white: #ffffff;
$gray-50: #f8fafc;
$gray-100: #f1f5f9;
$gray-200: #e2e8f0;
$gray-300: #cbd5e1;
$gray-400: #94a3b8;
$gray-500: #64748b;
$gray-600: #475569;
$gray-700: #334155;
$gray-800: #1e293b;
$gray-900: #0f172a;

/* 背景色系 */
$bg-primary: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
$bg-secondary: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
$bg-card: rgba(255, 255, 255, 0.9);
$bg-card-hover: rgba(255, 255, 255, 0.95);
$bg-glass: rgba(255, 255, 255, 0.85);

/* 文字颜色 */
$text-primary: #0f172a;
$text-secondary: #475569;
$text-muted: #94a3b8;
$text-inverse: #ffffff;

/* ================== 尺寸系统 ================== */

/* 字体大小 */
$font-xs: 20rpx;
$font-sm: 24rpx;
$font-base: 28rpx;
$font-lg: 32rpx;
$font-xl: 36rpx;
$font-2xl: 48rpx;
$font-3xl: 60rpx;
$font-4xl: 72rpx;

/* 圆角系统 */
$radius-sm: 8rpx;
$radius-base: 12rpx;
$radius-lg: 16rpx;
$radius-xl: 20rpx;
$radius-2xl: 24rpx;
$radius-full: 9999rpx;

/* 间距系统 */
$space-xs: 8rpx;
$space-sm: 12rpx;
$space-base: 16rpx;
$space-lg: 24rpx;
$space-xl: 32rpx;
$space-2xl: 48rpx;
$space-3xl: 64rpx;

/* 阴影系统 */
$shadow-sm: 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
$shadow-base: 0 4rpx 6rpx rgba(0, 0, 0, 0.07);
$shadow-lg: 0 10rpx 15rpx rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20rpx 25rpx rgba(0, 0, 0, 0.1);
$shadow-2xl: 0 25rpx 50rpx rgba(0, 0, 0, 0.25);

/* ================== 状态色系 ================== */

/* 辐射监测状态色 */
$radiation-safe: #10b981;
$radiation-safe-bg: rgba(16, 185, 129, 0.1);
$radiation-safe-border: rgba(16, 185, 129, 0.2);

$radiation-warning: #f59e0b;
$radiation-warning-bg: rgba(245, 158, 11, 0.1);
$radiation-warning-border: rgba(245, 158, 11, 0.2);

$radiation-danger: #ef4444;
$radiation-danger-bg: rgba(239, 68, 68, 0.1);
$radiation-danger-border: rgba(239, 68, 68, 0.2);

/* ================== 组件变量 ================== */

/* 卡片 */
$card-bg: $bg-card;
$card-border: rgba(226, 232, 240, 0.8);
$card-shadow: $shadow-lg;
$card-radius: $radius-xl;

/* 按钮 */
$btn-radius: $radius-lg;
$btn-shadow: $shadow-base;

/* 输入框 */
$input-bg: $white;
$input-border: $gray-300;
$input-border-focus: $primary-color;
$input-radius: $radius-base;

/* ================== 动画变量 ================== */
$transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
$transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

/* ================== 兼容性变量 ================== */

/* 兼容原有uni-app变量 */
$uni-color-primary: $primary-color;
$uni-color-success: $success-color;
$uni-color-warning: $warning-color;
$uni-color-error: $danger-color;

$uni-text-color: $text-primary;
$uni-text-color-inverse: $text-inverse;
$uni-text-color-grey: $text-muted;
$uni-text-color-placeholder: $gray-400;
$uni-text-color-disable: $gray-300;

$uni-bg-color: $white;
$uni-bg-color-grey: $gray-100;
$uni-bg-color-hover: $gray-50;
$uni-bg-color-mask: rgba(15, 23, 42, 0.5);

$uni-border-color: $gray-300;

$uni-font-size-sm: $font-sm;
$uni-font-size-base: $font-base;
$uni-font-size-lg: $font-lg;

$uni-border-radius-sm: $radius-sm;
$uni-border-radius-base: $radius-base;
$uni-border-radius-lg: $radius-lg;
$uni-border-radius-circle: 50%;

$uni-spacing-row-sm: $space-xs;
$uni-spacing-row-base: $space-base;
$uni-spacing-row-lg: $space-lg;

$uni-spacing-col-sm: $space-xs;
$uni-spacing-col-base: $space-base;
$uni-spacing-col-lg: $space-lg;

$uni-opacity-disabled: 0.5;

/* 文章场景相关 */
$uni-color-title: $text-primary;
$uni-font-size-title: $font-xl;
$uni-color-subtitle: $text-secondary;
$uni-font-size-subtitle: $font-lg;
$uni-color-paragraph: $text-secondary;
$uni-font-size-paragraph: $font-base;